import { notification } from 'antd';
import { basisBackendUrl, GET } from '../../utils/api/basicURL';

// 定义 API 响应接口
interface SatelliteChooseMethodResponse {
  algorithm: string;
  full_config: {
    satellite_choosing_algorithm: string;
  };
  supported_algorithms: string[];
}

// 算法名称映射（后端值到中文名称）
const ALGORITHM_DISPLAY_MAPPING: Record<string, string> = {
  'closest_satellite': '最近卫星',
  'north_first': '北向优先',
  'random': '随机选择',
  'south_first': '南向优先'
};

/**
 * 获取卫星选择算法配置
 * @returns Promise<SatelliteChooseMethodResponse | null> 返回算法配置数据或null
 */
export const getSatelliteChooseMethod = async (): Promise<SatelliteChooseMethodResponse | null> => {
  try {
    // 构建API URL
    const apiUrl = basisBackendUrl + '/get_satellite_choose_method';
    
    console.log('获取卫星选择算法配置:', apiUrl);

    // 调用后端API
    const response = await GET<SatelliteChooseMethodResponse>(apiUrl);
    
    console.log('卫星选择算法配置获取成功:', response);
    return response;
    
  } catch (error) {
    console.error('获取卫星选择算法配置异常:', error);
    notification.error({
      message: '获取失败',
      description: '无法获取卫星选择算法配置，请检查网络连接'
    });
    return null;
  }
};

/**
 * 获取算法的显示名称
 * @param algorithm 后端算法值
 * @returns 算法的中文显示名称
 */
export const getAlgorithmDisplayName = (algorithm: string): string => {
  return ALGORITHM_DISPLAY_MAPPING[algorithm] || algorithm;
};

/**
 * 获取所有支持的算法选项（用于下拉框）
 * @returns Promise<Array<{value: string, label: string}> | null> 返回算法选项数组或null
 */
export const getSupportedAlgorithmOptions = async (): Promise<Array<{value: string, label: string}> | null> => {
  try {
    const response = await getSatelliteChooseMethod();
    if (response && response.supported_algorithms) {
      return response.supported_algorithms.map(algorithm => ({
        value: algorithm,
        label: getAlgorithmDisplayName(algorithm)
      }));
    }
    return null;
  } catch (error) {
    console.error('获取支持的算法选项异常:', error);
    return null;
  }
};
