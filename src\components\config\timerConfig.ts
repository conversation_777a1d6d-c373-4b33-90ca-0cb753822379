/**
 * 定时器配置文件
 * 集中管理项目中所有定时器的频率参数
 * 
 * <AUTHOR> Assistant
 * @created 2024
 */

// ===== 定时器配置接口定义 =====

/**
 * 定时器配置接口
 * 定义所有定时器相关的时间参数
 */
export interface TimerConfig {
  // 数据获取相关定时器
  dataFetch: {
    /** 主时钟同步间隔 - main.tsx中向后端发送当前时间的频率 */
    MAIN_CLOCK_SYNC_INTERVAL: number;
    /** 卫星信息列表更新间隔 - satelliteInfoList.tsx中获取卫星数据的频率 */
    SATELLITE_LIST_UPDATE_INTERVAL: number;
    /** 卫星详细信息更新间隔 - satelliteInfo.tsx中获取卫星/地面站详细数据的频率 */
    SATELLITE_DETAIL_UPDATE_INTERVAL: number;
    /** 网络状态图表更新间隔 - satelliteNumberChart.tsx中获取网络状态数据的频率 */
    NETWORK_STATE_CHART_UPDATE_INTERVAL: number;
    /** 链路状态测试更新间隔 - LinkStatusAPITest.tsx中刷新链路状态的频率 */
    LINK_STATUS_TEST_UPDATE_INTERVAL: number;
    /** 旧版卫星信息更新间隔 - temp/satelliteInfo.js中自动更新的频率 */
    LEGACY_SATELLITE_INFO_UPDATE_INTERVAL: number;
    /** 鼠标悬停链路状态更新间隔 - mouseHandlers.ts中悬停时更新链路状态的频率 */
    MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL: number;
  };
  
  // 延迟相关定时器
  delays: {
    /** CZML数据清除延迟 - clearCzmlData.ts中等待渲染循环停止的时间 */
    CZML_CLEAR_DELAY: number;
    /** 测试等待延迟 - test.tsx中等待操作完成的时间 */
    TEST_WAIT_DELAY: number;
  };
  
  // API超时配置
  apiTimeouts: {
    /** 默认API请求超时时间 */
    DEFAULT_API_TIMEOUT: number;
  };
}

// ===== 默认配置值 =====

/**
 * 默认定时器配置
 * 包含项目中所有定时器的默认时间参数
 */
export const DEFAULT_TIMER_CONFIG: TimerConfig = {
  dataFetch: {
    // 主时钟同步 - 每500ms向后端发送当前时间
    MAIN_CLOCK_SYNC_INTERVAL: 500,
    
    // 卫星信息列表 - 每800ms获取卫星数据
    SATELLITE_LIST_UPDATE_INTERVAL: 800,
    
    // 卫星详细信息 - 每800ms获取详细数据（与satelliteData模块保持一致）
    SATELLITE_DETAIL_UPDATE_INTERVAL: 800,
    
    // 网络状态图表 - 每200ms获取网络状态（高频更新以保证实时性）
    NETWORK_STATE_CHART_UPDATE_INTERVAL: 800,
    
    // 链路状态测试 - 每2000ms刷新链路状态
    LINK_STATUS_TEST_UPDATE_INTERVAL: 2000,
    
    // 旧版卫星信息 - 每2000ms自动更新（保持与原有逻辑一致）
    LEGACY_SATELLITE_INFO_UPDATE_INTERVAL: 2000,

    // 鼠标悬停链路状态 - 每200ms更新链路状态（保证悬停时的实时性）
    MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL: 800,
  },
  
  delays: {
    // CZML清除延迟 - 等待200ms确保渲染循环完全停止
    CZML_CLEAR_DELAY: 200,
    
    // 测试等待延迟 - 等待300ms让操作完成
    TEST_WAIT_DELAY: 300,
  },
  
  apiTimeouts: {
    // 默认API超时时间 - 1000秒（与现有配置保持一致）
    DEFAULT_API_TIMEOUT: 1000000,
  },
};

// ===== 导出便捷常量 =====

/**
 * 当前使用的定时器配置
 * 可以通过修改这个变量来切换不同的配置
 */
export const TIMER_CONFIG = DEFAULT_TIMER_CONFIG;

// 为了向后兼容和便于使用，导出常用的定时器常量
export const {
  MAIN_CLOCK_SYNC_INTERVAL,
  SATELLITE_LIST_UPDATE_INTERVAL,
  SATELLITE_DETAIL_UPDATE_INTERVAL,
  NETWORK_STATE_CHART_UPDATE_INTERVAL,
  LINK_STATUS_TEST_UPDATE_INTERVAL,
  LEGACY_SATELLITE_INFO_UPDATE_INTERVAL,
  MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL,
} = TIMER_CONFIG.dataFetch;

export const {
  CZML_CLEAR_DELAY,
  TEST_WAIT_DELAY,
} = TIMER_CONFIG.delays;

export const {
  DEFAULT_API_TIMEOUT,
} = TIMER_CONFIG.apiTimeouts;

// ===== 工具函数 =====

/**
 * 获取指定定时器的配置值
 * @param timerName 定时器名称
 * @returns 定时器配置值（毫秒）
 */
export function getTimerInterval(timerName: keyof TimerConfig['dataFetch']): number {
  return TIMER_CONFIG.dataFetch[timerName];
}

/**
 * 获取指定延迟的配置值
 * @param delayName 延迟名称
 * @returns 延迟配置值（毫秒）
 */
export function getDelayTime(delayName: keyof TimerConfig['delays']): number {
  return TIMER_CONFIG.delays[delayName];
}

/**
 * 更新定时器配置（用于运行时动态调整）
 * @param newConfig 新的配置对象（部分更新）
 */
export function updateTimerConfig(newConfig: Partial<TimerConfig>): void {
  Object.assign(TIMER_CONFIG, newConfig);
}

// ===== 配置验证 =====

/**
 * 验证定时器配置的有效性
 * @param config 要验证的配置
 * @returns 验证结果
 */
export function validateTimerConfig(config: TimerConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 检查所有数据获取间隔是否为正数
  Object.entries(config.dataFetch).forEach(([key, value]) => {
    if (typeof value !== 'number' || value <= 0) {
      errors.push(`dataFetch.${key} 必须是正数，当前值: ${value}`);
    }
  });
  
  // 检查所有延迟是否为非负数
  Object.entries(config.delays).forEach(([key, value]) => {
    if (typeof value !== 'number' || value < 0) {
      errors.push(`delays.${key} 必须是非负数，当前值: ${value}`);
    }
  });
  
  // 检查API超时配置
  Object.entries(config.apiTimeouts).forEach(([key, value]) => {
    if (typeof value !== 'number' || value <= 0) {
      errors.push(`apiTimeouts.${key} 必须是正数，当前值: ${value}`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// 在模块加载时验证默认配置
const validation = validateTimerConfig(DEFAULT_TIMER_CONFIG);
if (!validation.valid) {
  console.warn('定时器配置验证失败:', validation.errors);
}
